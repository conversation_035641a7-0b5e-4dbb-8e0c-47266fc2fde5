package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// PolicyName represents a policy name
type PolicyName string

// PolicyValue represents a policy value
type PolicyValue interface{}

// IPolicy represents a policy interface
type IPolicy struct {
	Name PolicyName `json:"name"`
}

// IPolicyService is the interface for the policy service.
type IPolicyService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// OnDidChange returns an event that fires when policies change
	OnDidChange() basecommon.Event[[]string]

	// UpdatePolicyDefinitions updates the policy definitions
	UpdatePolicyDefinitions(definitions map[string]*PolicyDefinition) (map[string]interface{}, error)

	// GetPolicyValue gets a policy value by name
	GetPolicyValue(name string) interface{}

	// Serialize serializes the policies
	Serialize() map[string]struct {
		Definition PolicyDefinition
		Value      interface{}
	}

	// GetPolicyDefinitions returns the policy definitions
	GetPolicyDefinitions() map[string]PolicyDefinition
}

// PolicyDefinition represents a policy definition.
type PolicyDefinition struct {
	Type           string      `json:"type"`
	PreviewFeature *bool       `json:"previewFeature,omitempty"`
	DefaultValue   interface{} `json:"defaultValue,omitempty"`
}

// AbstractPolicyService provides a base implementation of IPolicyService
type AbstractPolicyService struct {
	*basecommon.Disposable

	// PolicyDefinitions holds the policy definitions
	PolicyDefinitions map[string]*PolicyDefinition

	// policies holds the current policy values
	policies map[string]PolicyValue

	// onDidChange event emitter
	onDidChange *basecommon.Emitter[[]string]

	// mutex for thread safety
	mu sync.RWMutex
}

// NewAbstractPolicyService creates a new AbstractPolicyService
func NewAbstractPolicyService() *AbstractPolicyService {
	disposable := basecommon.NewDisposable()
	onDidChange := basecommon.NewEmitter[[]string]()

	aps := &AbstractPolicyService{
		Disposable:        disposable,
		PolicyDefinitions: make(map[string]*PolicyDefinition),
		policies:          make(map[string]PolicyValue),
		onDidChange:       onDidChange,
	}

	// Register the emitter for disposal
	disposable.Register(onDidChange)

	return aps
}

// ServiceBrand implements IPolicyService
func (aps *AbstractPolicyService) ServiceBrand() interface{} {
	return nil
}

// OnDidChange implements IPolicyService
func (aps *AbstractPolicyService) OnDidChange() basecommon.Event[[]string] {
	return aps.onDidChange.Event()
}

// UpdatePolicyDefinitions implements IPolicyService
func (aps *AbstractPolicyService) UpdatePolicyDefinitions(policyDefinitions map[string]*PolicyDefinition) (map[string]interface{}, error) {
	aps.mu.Lock()
	defer aps.mu.Unlock()

	size := len(aps.PolicyDefinitions)

	// Merge new definitions with existing ones
	for key, def := range policyDefinitions {
		aps.PolicyDefinitions[key] = def
	}

	// If new definitions were added, call the abstract method
	if size != len(aps.PolicyDefinitions) {
		err := aps.updatePolicyDefinitions(aps.PolicyDefinitions)
		if err != nil {
			return nil, err
		}
	}

	// Return current policy values
	result := make(map[string]interface{})
	for name, value := range aps.policies {
		result[name] = value
	}

	return result, nil
}

// GetPolicyValue implements IPolicyService
func (aps *AbstractPolicyService) GetPolicyValue(name string) interface{} {
	aps.mu.RLock()
	defer aps.mu.RUnlock()

	return aps.policies[name]
}

// Serialize implements IPolicyService
func (aps *AbstractPolicyService) Serialize() map[string]struct {
	Definition PolicyDefinition
	Value      interface{}
} {
	aps.mu.RLock()
	defer aps.mu.RUnlock()

	result := make(map[string]struct {
		Definition PolicyDefinition
		Value      interface{}
	})

	for name, definition := range aps.PolicyDefinitions {
		value := aps.policies[name]
		result[name] = struct {
			Definition PolicyDefinition
			Value      interface{}
		}{
			Definition: *definition,
			Value:      value,
		}
	}

	return result
}

// GetPolicyDefinitions implements IPolicyService
func (aps *AbstractPolicyService) GetPolicyDefinitions() map[string]PolicyDefinition {
	aps.mu.RLock()
	defer aps.mu.RUnlock()

	result := make(map[string]PolicyDefinition)
	for name, def := range aps.PolicyDefinitions {
		result[name] = *def
	}

	return result
}

// updatePolicyDefinitions is an abstract method that subclasses must implement
func (aps *AbstractPolicyService) updatePolicyDefinitions(policyDefinitions map[string]*PolicyDefinition) error {
	// Default implementation does nothing
	return nil
}

// SetPolicyValue sets a policy value (protected method for subclasses)
func (aps *AbstractPolicyService) SetPolicyValue(name string, value PolicyValue) {
	aps.mu.Lock()
	defer aps.mu.Unlock()

	if value == nil {
		delete(aps.policies, name)
	} else {
		aps.policies[name] = value
	}
}

// FireOnDidChange fires the onDidChange event (protected method for subclasses)
func (aps *AbstractPolicyService) FireOnDidChange(changedPolicies []string) {
	aps.onDidChange.Fire(changedPolicies)
}

// IPolicyServiceId is the service identifier for IPolicyService
var IPolicyServiceId = instantiationcommon.CreateDecorator[IPolicyService]("policy")
