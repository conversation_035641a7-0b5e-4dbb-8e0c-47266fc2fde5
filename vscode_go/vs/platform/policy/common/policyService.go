package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// NullPolicyService is a no-op implementation of IPolicyService.
type NullPolicyService struct{}

// NewNullPolicyService creates a new NullPolicyService.
func NewNullPolicyService() *NullPolicyService {
	return &NullPolicyService{}
}

// ServiceBrand implements IPolicyService
func (s *NullPolicyService) ServiceBrand() interface{} {
	return nil
}

// OnDidChange returns a no-op event.
func (s *NullPolicyService) OnDidChange() basecommon.Event[[]string] {
	return basecommon.EventNone[[]string]()
}

// UpdatePolicyDefinitions does nothing and returns an empty map.
func (s *NullPolicyService) UpdatePolicyDefinitions(definitions map[string]*PolicyDefinition) (map[string]interface{}, error) {
	return make(map[string]interface{}), nil
}

// GetPolicyValue always returns nil.
func (s *NullPolicyService) GetPolicyValue(name string) interface{} {
	return nil
}

// Serialize always returns nil.
func (s *NullPolicyService) Serialize() map[string]struct {
	Definition PolicyDefinition
	Value      interface{}
} {
	return nil
}

// GetPolicyDefinitions returns an empty map.
func (s *NullPolicyService) GetPolicyDefinitions() map[string]PolicyDefinition {
	return make(map[string]PolicyDefinition)
}
