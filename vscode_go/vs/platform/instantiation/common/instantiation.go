/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"reflect"
	"sync"
)

// BrandedService represents a service with a brand marker
type BrandedService interface {
	ServiceBrand() interface{}
}

// ServiceIdentifier uniquely identifies a service type
type ServiceIdentifier[T any] struct {
	id   string
	name string
}

// String returns the service identifier as a string
func (si ServiceIdentifier[T]) String() string {
	return si.name
}

// ID returns the service ID
func (si ServiceIdentifier[T]) ID() string {
	return si.id
}

// ServicesAccessor provides access to services
type ServicesAccessor interface {
	Get(id interface{}) interface{}
}

// IInstantiationService provides service instantiation capabilities
type IInstantiationService interface {
	BrandedService

	// CreateInstance creates an instance of the specified type
	CreateInstance(ctor interface{}, args ...interface{}) (interface{}, error)

	// InvokeFunction calls a function with service accessor
	InvokeFunction(fn interface{}, args ...interface{}) (interface{}, error)

	// CreateChild creates a child instantiation service
	CreateChild(services *ServiceCollection) IInstantiationService

	// Dispose disposes the instantiation service
	Dispose()
}

// serviceIds keeps track of all registered service identifiers
var serviceIds = make(map[string]ServiceIdentifier[interface{}])
var serviceIdsMu sync.RWMutex

// CreateDecorator creates a service identifier decorator
func CreateDecorator[T any](serviceId string) ServiceIdentifier[T] {
	serviceIdsMu.Lock()
	defer serviceIdsMu.Unlock()

	if existing, exists := serviceIds[serviceId]; exists {
		// Return existing service identifier with proper type
		return ServiceIdentifier[T]{
			id:   existing.id,
			name: existing.name,
		}
	}

	id := ServiceIdentifier[T]{
		id:   serviceId,
		name: serviceId,
	}

	// Store as interface{} type for lookup
	serviceIds[serviceId] = ServiceIdentifier[interface{}]{
		id:   serviceId,
		name: serviceId,
	}

	return id
}

// InstantiationService implements IInstantiationService
type InstantiationService struct {
	services *ServiceCollection
	parent   IInstantiationService
	children []IInstantiationService
	disposed bool
	mu       sync.RWMutex
}

// NewInstantiationService creates a new instantiation service
func NewInstantiationService(services *ServiceCollection, parent IInstantiationService) *InstantiationService {
	if services == nil {
		services = NewServiceCollection()
	}

	return &InstantiationService{
		services: services,
		parent:   parent,
		children: make([]IInstantiationService, 0),
	}
}

// ServiceBrand implements BrandedService
func (is *InstantiationService) ServiceBrand() interface{} {
	return nil
}

// CreateInstance creates an instance of the specified type
func (is *InstantiationService) CreateInstance(ctor interface{}, args ...interface{}) (interface{}, error) {
	is.mu.RLock()
	if is.disposed {
		is.mu.RUnlock()
		return nil, fmt.Errorf("instantiation service is disposed")
	}
	is.mu.RUnlock()

	// Use reflection to create instance
	ctorType := reflect.TypeOf(ctor)
	if ctorType.Kind() != reflect.Func {
		return nil, fmt.Errorf("constructor must be a function")
	}

	// Prepare arguments for constructor
	ctorValue := reflect.ValueOf(ctor)
	numIn := ctorType.NumIn()
	callArgs := make([]reflect.Value, numIn)

	// Fill in provided arguments
	for i := 0; i < len(args) && i < numIn; i++ {
		callArgs[i] = reflect.ValueOf(args[i])
	}

	// Fill in remaining arguments with services
	for i := len(args); i < numIn; i++ {
		paramType := ctorType.In(i)
		service := is.getServiceByType(paramType)
		if service != nil {
			callArgs[i] = reflect.ValueOf(service)
		} else {
			// Create zero value if service not found
			callArgs[i] = reflect.Zero(paramType)
		}
	}

	// Call constructor
	results := ctorValue.Call(callArgs)
	if len(results) == 0 {
		return nil, fmt.Errorf("constructor returned no values")
	}

	return results[0].Interface(), nil
}

// InvokeFunction calls a function with service accessor
func (is *InstantiationService) InvokeFunction(fn interface{}, args ...interface{}) (interface{}, error) {
	is.mu.RLock()
	if is.disposed {
		is.mu.RUnlock()
		return nil, fmt.Errorf("instantiation service is disposed")
	}
	is.mu.RUnlock()

	fnType := reflect.TypeOf(fn)
	if fnType.Kind() != reflect.Func {
		return nil, fmt.Errorf("fn must be a function")
	}

	fnValue := reflect.ValueOf(fn)
	numIn := fnType.NumIn()
	callArgs := make([]reflect.Value, numIn)

	// First argument should be ServicesAccessor
	if numIn > 0 {
		callArgs[0] = reflect.ValueOf(is)
	}

	// Fill in remaining arguments
	for i := 1; i < numIn && i-1 < len(args); i++ {
		callArgs[i] = reflect.ValueOf(args[i-1])
	}

	// Call function
	results := fnValue.Call(callArgs)
	if len(results) == 0 {
		return nil, nil
	}

	return results[0].Interface(), nil
}

// CreateChild creates a child instantiation service
func (is *InstantiationService) CreateChild(services *ServiceCollection) IInstantiationService {
	is.mu.Lock()
	defer is.mu.Unlock()

	child := NewInstantiationService(services, is)
	is.children = append(is.children, child)
	return child
}

// Dispose disposes the instantiation service
func (is *InstantiationService) Dispose() {
	is.mu.Lock()
	defer is.mu.Unlock()

	if is.disposed {
		return
	}

	is.disposed = true

	// Dispose all children
	for _, child := range is.children {
		child.Dispose()
	}
	is.children = nil
}

// Get implements ServicesAccessor
func (is *InstantiationService) Get(id interface{}) interface{} {
	is.mu.RLock()
	defer is.mu.RUnlock()

	if is.disposed {
		return nil
	}

	// Try to get from local services first
	if serviceId, ok := id.(ServiceIdentifier[interface{}]); ok {
		if service := is.services.Get(serviceId); service != nil {
			return service
		}
	}

	// Try parent if available
	if is.parent != nil {
		if accessor, ok := is.parent.(ServicesAccessor); ok {
			return accessor.Get(id)
		}
	}

	return nil
}

// getServiceByType attempts to find a service by its type
func (is *InstantiationService) getServiceByType(serviceType reflect.Type) interface{} {
	is.mu.RLock()
	defer is.mu.RUnlock()

	// This is a simplified implementation
	// In a real implementation, you'd maintain a type registry
	for _, service := range is.services.entries {
		if reflect.TypeOf(service) == serviceType {
			return service
		}
	}

	// Try parent
	if is.parent != nil {
		if parentImpl, ok := is.parent.(*InstantiationService); ok {
			return parentImpl.getServiceByType(serviceType)
		}
	}

	return nil
}

// Global instantiation service identifier
var IInstantiationServiceId = CreateDecorator[IInstantiationService]("instantiationService")
