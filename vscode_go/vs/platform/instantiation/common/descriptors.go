/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "reflect"

// SyncDescriptor describes a service constructor with static arguments
type SyncDescriptor[T any] struct {
	Ctor                         interface{}
	StaticArguments              []interface{}
	SupportsDelayedInstantiation bool
}

// NewSyncDescriptor creates a new SyncDescriptor
func NewSyncDescriptor[T any](ctor interface{}, staticArguments []interface{}, supportsDelayedInstantiation bool) *SyncDescriptor[T] {
	if staticArguments == nil {
		staticArguments = []interface{}{}
	}
	
	return &SyncDescriptor[T]{
		Ctor:                         ctor,
		StaticArguments:              staticArguments,
		SupportsDelayedInstantiation: supportsDelayedInstantiation,
	}
}

// SyncDescriptor0 represents a constructor with no arguments
type SyncDescriptor0[T any] interface {
	GetCtor() reflect.Type
}

// syncDescriptor0Impl implements SyncDescriptor0
type syncDescriptor0Impl[T any] struct {
	ctor reflect.Type
}

// NewSyncDescriptor0 creates a new SyncDescriptor0
func NewSyncDescriptor0[T any](ctor interface{}) SyncDescriptor0[T] {
	return &syncDescriptor0Impl[T]{
		ctor: reflect.TypeOf(ctor),
	}
}

// GetCtor returns the constructor type
func (s *syncDescriptor0Impl[T]) GetCtor() reflect.Type {
	return s.ctor
}
