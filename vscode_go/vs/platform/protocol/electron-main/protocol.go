/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IIPCObjectUrl represents an IPC object URL that can be disposed
type IIPCObjectUrl[T any] interface {
	basecommon.IDisposable
	
	// Resource returns a URI that a renderer can use to retrieve the
	// object via ipcRenderer.invoke(resource.toString())
	Resource() *basecommon.URI
	
	// Update allows to update the value of the object after it
	// has been created
	Update(obj T)
}

// IProtocolMainService provides protocol main service functionality
type IProtocolMainService interface {
	// ServiceBrand returns the service brand marker
	ServiceBrand() interface{}
	
	// CreateIPCObjectUrl allows to make an object accessible to a renderer
	// via ipcRenderer.invoke(resource.toString())
	CreateIPCObjectUrl() IIPCObjectUrl[interface{}]
	
	// AddValidFileRoot adds a path as root to the list of allowed
	// resources for file access
	AddValidFileRoot(root string) basecommon.IDisposable
}

// IProtocolMainServiceId is the service identifier for IProtocolMainService
var IProtocolMainServiceId = instantiationcommon.CreateDecorator[IProtocolMainService]("protocolMainService")
