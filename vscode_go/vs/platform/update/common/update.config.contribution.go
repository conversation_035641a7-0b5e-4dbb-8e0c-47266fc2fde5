/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	nlscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	registrycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/registry/common"
)

func init() {
	configurationRegistry := registrycommon.Registry.As(configurationcommon.Extensions.Configuration)
	if registry, ok := configurationRegistry.(configurationcommon.IConfigurationRegistry); ok {
		registry.RegisterConfiguration(&configurationcommon.IConfigurationNode{
			ID:    stringPtr("update"),
			Order: 15,
			Title: stringPtr(nlscommon.Localize("updateConfigurationTitle", "Update")),
			Type:  "object",
			Properties: map[string]*configurationcommon.IConfigurationPropertySchema{
				"update.mode": {
					IJSONSchema: &basecommon.IJSONSchema{
						Type:        "string",
						Enum:        []interface{}{"none", "manual", "start", "default"},
						Default:     "default",
						Description: stringPtr(nlscommon.Localize("updateMode", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")),
						EnumDescriptions: []string{
							nlscommon.Localize("none", "Disable updates."),
							nlscommon.Localize("manual", "Disable automatic background update checks. Updates will be available if you manually check for updates."),
							nlscommon.Localize("start", "Check for updates only on startup. Disable automatic background update checks."),
							nlscommon.Localize("default", "Enable automatic update checks. Code will check for updates automatically and periodically."),
						},
					},
					Scope: configurationcommon.ApplicationScope,
					Tags:  []string{"usesOnlineServices"},
					Policy: &configurationcommon.IPolicy{
						Name: "UpdateMode",
					},
				},
				"update.channel": {
					IJSONSchema: &basecommon.IJSONSchema{
						Type:               "string",
						Default:            "default",
						Description:        stringPtr(nlscommon.Localize("updateMode", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")),
						DeprecationMessage: stringPtr(nlscommon.Localize("deprecated", "This setting is deprecated, please use '{0}' instead.", "update.mode")),
					},
					Scope: configurationcommon.ApplicationScope,
				},
				"update.enableWindowsBackgroundUpdates": {
					IJSONSchema: &basecommon.IJSONSchema{
						Type:        "boolean",
						Default:     true,
						Title:       stringPtr(nlscommon.Localize("enableWindowsBackgroundUpdatesTitle", "Enable Background Updates on Windows")),
						Description: stringPtr(nlscommon.Localize("enableWindowsBackgroundUpdates", "Enable to download and install new VS Code versions in the background on Windows.")),
					},
					Scope:    configurationcommon.ApplicationScope,
					Included: boolPtr(basecommon.IsWindows && !basecommon.IsWeb),
				},
				"update.showReleaseNotes": {
					IJSONSchema: &basecommon.IJSONSchema{
						Type:        "boolean",
						Default:     true,
						Description: stringPtr(nlscommon.Localize("showReleaseNotes", "Show Release Notes after an update. The Release Notes are fetched from a Microsoft online service.")),
					},
					Scope: configurationcommon.ApplicationScope,
					Tags:  []string{"usesOnlineServices"},
				},
			},
		})
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}
